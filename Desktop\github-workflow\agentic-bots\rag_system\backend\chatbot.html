<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Chatbot Widget</title>
    
    <!-- These are required for the widget to function in this standalone file. -->
    <!-- Google Fonts and Icons -->
    <link rel="stylesheet"
        href="https://fonts.googleapis.com/css2?family=Material+Symbols+Rounded:opsz,wght,FILL,GRAD@20..48,100..700,0..1,-50..200" />
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600&display=swap" rel="stylesheet">

    <!-- React and Babel CDNs -->
    <script src="https://unpkg.com/react@18/umd/react.development.js"></script>
    <script src="https://unpkg.com/react-dom@18/umd/react-dom.development.js"></script>
    <script src="https://unpkg.com/@babel/standalone/babel.min.js"></script>

    <!-- CSS Styles for the Widget -->
    <style>
        /* All styles are scoped to the #chatbot-widget container to prevent conflicts with host pages. */
        #chatbot-widget {
            --primary-color: #6D4FC2;
            --secondary-color: #F6F2FF;
            --header-bg: var(--primary-color);
            --widget-bg: #fff;
            --body-bg: linear-gradient(to top, #F4F0FF, #DACDFF);
            --text-color: #333;
            --text-color-light: #fff;
            --border-color: #ddd;
            --input-bg: #fff;
            --scrollbar-color: #DDD3F9;

            font-family: "Inter", sans-serif;
        }

        #chatbot-widget.dark {
            --primary-color: #7F5AF0;
            --secondary-color: #2c2c2e;
            --header-bg: #1e1e1e;
            --widget-bg: #242424;
            --body-bg: #121212;
            --text-color: #f0f0f0;
            --text-color-light: #f0f0f0;
            --border-color: #444;
            --input-bg: #333;
            --scrollbar-color: #4a4a4a;
        }
        
        /* This is a simple button for demo purposes to show how to toggle dark mode */
        #theme-toggler {
             padding: 10px 15px;
             border-radius: 8px;
             border: 1px solid #ccc;
             cursor: pointer;
             position: absolute;
             top: 20px;
             left: 20px;
             background: #fff;
             z-index: 1000;
        }

        /* Set the background for the host page body */
        body {
            background: var(--body-bg);
            transition: background 0.3s ease;
        }
        
        #chatbot-widget * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        /* --- Icon Font Fix --- */
        #chatbot-widget .material-symbols-rounded {
            font-family: 'Material Symbols Rounded', sans-serif;
            font-weight: normal;
            font-style: normal;
            font-size: 24px;
            line-height: 1;
            letter-spacing: normal;
            text-transform: none;
            display: inline-block;
            white-space: nowrap;
            word-wrap: normal;
            direction: ltr;
            -webkit-font-feature-settings: 'liga';
            -webkit-font-smoothing: antialiased;
            user-select: none;
        }

        /* --- Chatbot Toggler --- */
        #chatbot-widget #chatbot-toggler {
            position: fixed;
            right: 35px;
            bottom: 30px;
            height: 50px;
            width: 50px;
            border: none;
            outline: none;
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 50%;
            background: var(--primary-color);
            color: var(--text-color-light);
            transition: all 0.2s ease;
            z-index: 999;
        }

        #chatbot-widget .show-chatbot #chatbot-toggler {
            transform: rotate(90deg);
        }

        #chatbot-widget #chatbot-toggler span {
            position: absolute;
            transition: opacity 0.2s ease;
        }
        
        #chatbot-widget .show-chatbot #chatbot-toggler span:first-child,
        #chatbot-widget #chatbot-toggler span:last-child {
            opacity: 0;
        }

        #chatbot-widget .show-chatbot #chatbot-toggler span:last-child {
            opacity: 1;
        }

        /* --- Chatbot Popup --- */
        #chatbot-widget .chatbot-popup {
            position: fixed;
            right: 35px;
            bottom: 90px;
            width: 420px;
            background: var(--widget-bg);
            border-radius: 15px;
            box-shadow: 0 0 128px 0 rgba(0, 0, 0, 0.1),
                0 32px 64px -48px rgba(0, 0, 0, 0.5);
            transform: scale(0.5);
            opacity: 0;
            pointer-events: none;
            transform-origin: bottom right;
            transition: all 0.15s ease;
            color: var(--text-color);
        }

        #chatbot-widget .show-chatbot .chatbot-popup {
            transform: scale(1);
            opacity: 1;
            pointer-events: auto;
        }

        #chatbot-widget .chatbot-popup .chat-header {
            background: var(--header-bg);
            padding: 16px 20px;
            border-radius: 15px 15px 0 0;
            text-align: center;
            color: var(--text-color-light);
            display: flex;
            align-items: center;
            justify-content: space-between;
        }

        #chatbot-widget .chat-header .header-info {
            display: flex;
            align-items: center;
            gap: 10px;
        }

        #chatbot-widget .header-info .logo-text {
            font-size: 1.4rem;
            font-weight: 600;
        }
        
        #chatbot-widget .chat-header .chatbot-icon {
             background: var(--text-color-light);
             color: var(--primary-color);
        }

        #chatbot-widget .chat-header .close-btn {
            font-size: 1.9rem;
            cursor: pointer;
        }

        /* --- Chat Body --- */
        #chatbot-widget .chatbot-popup .chat-body {
            height: 510px;
            padding: 30px 20px 100px;
            overflow-y: auto;
            display: flex;
            flex-direction: column;
            gap: 20px;
            scrollbar-width: thin;
            scrollbar-color: var(--scrollbar-color) transparent;
        }

        #chatbot-widget .chat-body .message {
            display: flex;
            gap: 10px;
            align-items: flex-start;
        }

        #chatbot-widget .chat-body .message.bot-message {
            justify-content: flex-start;
        }

        #chatbot-widget .chat-body .message.user-message {
            flex-direction: row-reverse;
        }

        #chatbot-widget .chatbot-icon {
            height: 35px;
            width: 35px;
            color: var(--text-color-light);
            background: var(--primary-color);
            padding: 6px;
            border-radius: 50%;
            flex-shrink: 0;
        }

        #chatbot-widget .chat-body .message-text {
            padding: 12px 16px;
            max-width: 75%;
            word-wrap: break-word;
            white-space: pre-line;
            font-size: 0.95rem;
        }

        #chatbot-widget .chat-body .bot-message .message-text {
            background: var(--secondary-color);
            color: var(--text-color);
            border-radius: 12px 12px 12px 0;
        }

        #chatbot-widget .chat-body .user-message .message-text {
            background: var(--primary-color);
            color: var(--text-color-light);
            border-radius: 12px 12px 0 12px;
        }

        /* --- Chat Footer --- */
        #chatbot-widget .chatbot-popup .chat-footer {
            position: absolute;
            bottom: 0;
            width: 100%;
            background: var(--widget-bg);
            padding: 3px 20px 10px;
            border-top: 1px solid var(--border-color);
        }

        #chatbot-widget .chat-footer .chat-form {
            display: flex;
            align-items: center;
            gap: 10px;
            border: 1px solid var(--border-color);
            border-radius: 6px;
            padding: 5px 12px;
            background: var(--input-bg);
        }

        #chatbot-widget .chat-form .message-input {
            border: none;
            outline: none;
            width: 100%;
            padding: 10px 0;
            font-size: 0.95rem;
            background: none;
            color: var(--text-color);
        }

        #chatbot-widget .chat-form .send-btn {
            font-size: 1.75rem;
            cursor: pointer;
            color: var(--primary-color);
            background: none;
            border: none;
            outline: none;
        }

        /* --- Responsive Design --- */
        @media(max-width: 600px) {
            #chatbot-widget .chatbot-popup {
                right: 0;
                bottom: 0;
                width: 100%;
                height: 100%;
                border-radius: 0; /* Ensures sharp edges on mobile */
            }

            #chatbot-widget .chatbot-popup .chat-header {
                 border-radius: 0; /* Ensures header also has sharp edges */
            }

            #chatbot-widget .chatbot-popup .chat-body {
                height: 90%;
            }
            
            /* Hide the toggler button on mobile when the chat is open */
            #chatbot-widget .show-chatbot #chatbot-toggler {
                display: none;
            }
        }
    </style>
</head>

<body>
    <!-- 
      This button is for demonstration purposes.
      You can toggle dark mode by adding/removing the 'dark' class 
      to the #chatbot-widget container from your own site's logic.
    -->
    <button id="theme-toggler">Toggle Dark Mode</button>

    <!-- This is the container where the chatbot widget will be rendered. -->
    <div id="chatbot-widget"></div>

    <script type="text/babel">
        // --- React Components for the Widget---

        const ChatbotIcon = (props) => (
            <svg xmlns="http://www.w3.org/2000/svg" className="chatbot-icon" {...props} viewBox="0 0 24 24" fill="currentColor">
                <path d="M12 2C6.48 2 2 5.79 2 10.5c0 2.22 1.01 4.25 2.68 5.72l-1.68 1.68c-.14.14-.14.36 0 .**********.1.25.1s.18-.03.25-.1l1.93-1.93C7.06 17.58 9.42 18.5 12 18.5c5.52 0 10-3.79 10-8.5S17.52 2 12 2zm1 11h-2v-2h2v2zm0-4h-2V6h2v3z" />
            </svg>
        );

        const ChatMessage = ({ chat }) => {
            if (!chat) return null;
            
            const isBot = chat.role === 'model';
            const messageClass = `message ${isBot ? 'bot-message' : 'user-message'}`;

            return (
                <div className={messageClass}>
                    {isBot && <ChatbotIcon />}
                    <div className="message-text">{chat.text}</div>
                </div>
            );
        };

        const ChatForm = ({ onNewMessage }) => {
            const inputRef = React.useRef(null);

            const handleFormSubmit = (e) => {
                e.preventDefault();
                const userMessage = inputRef.current.value.trim();
                if (!userMessage) return;

                onNewMessage(userMessage);
                inputRef.current.value = "";
            };

            return (
                <div className="chat-footer">
                    <form className="chat-form" onSubmit={handleFormSubmit}>
                        <input
                            ref={inputRef}
                            className="message-input"
                            placeholder="Enter a message..."
                            required
                        />
                        <button type="submit" className="material-symbols-rounded send-btn">send</button>
                    </form>
                </div>
            );
        };

        const App = () => {
            const [showChatbot, setShowChatbot] = React.useState(false);
            const [chatHistory, setChatHistory] = React.useState([
                { role: "model", text: "Hey there �\nHow can I help you today?" }
            ]);
            const chatBodyRef = React.useRef(null);

            const handleNewMessage = (userMessage) => {
                // Update chat history with user's message
                setChatHistory(prev => [...prev, { role: "user", text: userMessage }]);
                
                // Show a "Thinking..." message from the bot
                setTimeout(() => {
                    const thinkingMessage = { role: "model", text: "Thinking..." };
                    setChatHistory(prev => [...prev, thinkingMessage]);
                    
                    // After a short delay, replace "Thinking..." with a canned response
                    setTimeout(() => {
                         setChatHistory(prev => {
                            const updatedHistory = prev.filter(msg => msg.text !== "Thinking...");
                            return [...updatedHistory, { role: "model", text: "This is a demo response! The API has been removed." }];
                         });
                    }, 1500);

                }, 600);
            };
            
            // Auto-scroll whenever chat history updates
            React.useEffect(() => {
                if (chatBodyRef.current) {
                    chatBodyRef.current.scrollTop = chatBodyRef.current.scrollHeight;
                }
            }, [chatHistory]);

            return (
                <div className={`app-container ${showChatbot ? 'show-chatbot' : ''}`}>
                    <button id="chatbot-toggler" onClick={() => setShowChatbot(prev => !prev)}>
                        <span className="material-symbols-rounded">mode_comment</span>
                        <span className="material-symbols-rounded">close</span>
                    </button>
                    
                    <div className="chatbot-popup">
                        <div className="chat-header">
                            <div className="header-info">
                                <ChatbotIcon />
                                <h2 className="logo-text">Chatbot</h2>
                            </div>
                            <span className="material-symbols-rounded close-btn" onClick={() => setShowChatbot(false)}>close</span>
                        </div>
                        <div className="chat-body" ref={chatBodyRef}>
                            {chatHistory.map((chat, index) => <ChatMessage key={index} chat={chat} />)}
                        </div>
                        <ChatForm onNewMessage={handleNewMessage} />
                    </div>
                </div>
            );
        };
        
        // --- Widget Initialization ---

        // Find the container and render the React App into it.
        const widgetContainer = document.getElementById('chatbot-widget');
        if(widgetContainer) {
             ReactDOM.createRoot(widgetContainer).render(<App />);
        } else {
             console.error("Chatbot widget container with id 'chatbot-widget' not found.");
        }
        
        // Logic for the demo theme toggler button
        const themeToggler = document.getElementById('theme-toggler');
        const body = document.querySelector('body');
        themeToggler.addEventListener('click', () => {
             widgetContainer.classList.toggle('dark');
             // Also toggle on body to update page background for the demo
             body.style.background = widgetContainer.classList.contains('dark') ? '#121212' : 'linear-gradient(to top, #F4F0FF, #DACDFF)';
        });


    </script>
</body>

</html>
�