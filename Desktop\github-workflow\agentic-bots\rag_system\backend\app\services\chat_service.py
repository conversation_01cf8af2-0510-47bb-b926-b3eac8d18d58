"""
Enhanced chat service with natural conversation, function calling, and RAG capabilities.
"""
import json
import re
from typing import List, Dict, Any, Optional, Tuple
import uuid
import asyncio
import httpx
from datetime import datetime

from langchain.text_splitter import RecursiveCharacterTextSplitter
from langchain_google_genai import GoogleGenerativeAIEmbeddings, ChatGoogleGenerativeAI
from langchain_community.vectorstores import SupabaseVectorStore
from langchain_core.documents import Document
from langchain_core.messages import HumanMessage, SystemMessage

from app.config import settings
from app.models.schemas import (
    ChatMessageResponse, ConversationIntent, ActionStatus,
    FunctionCall, FunctionCallResult, SearchResult, DynamicVariable,
    ChatRequest
)
from app.database import get_supabase_client
import structlog

logger = structlog.get_logger()


class CustomSupabaseVectorStore(SupabaseVectorStore):
    """Enhanced vector store with chatbot filtering."""
    
    def __init__(self, client, embedding, table_name: str, query_name: str):
        super().__init__(client=client, embedding=embedding, table_name=table_name, query_name=query_name)
        self.query_name = query_name

    def similarity_search(self, query: str, chatbot_id: str, k: int = 5, **kwargs) -> List[Document]:
        """Perform similarity search filtered by chatbot_id."""
        query_embedding = self._embedding.embed_query(query)
        
        params = {
            'query_embedding': query_embedding,
            'match_count': k,
            'p_chatbot_id': chatbot_id
        }
        
        try:
            res = self._client.rpc(self.query_name, params).execute()
            return [
                Document(
                    page_content=row["content"],
                    metadata=row["metadata"] or {},
                )
                for row in res.data
            ]
        except Exception as e:
            logger.error(f"Error in similarity search: {e}")
            return []


class ChatService:
    """Enhanced chat service with natural conversation and function calling."""
    
    def __init__(self):
        self.embeddings = GoogleGenerativeAIEmbeddings(
            model=settings.embedding_model,
            google_api_key=settings.google_api_key
        )
        self.llm = ChatGoogleGenerativeAI(
            model=settings.gemini_model,
            temperature=0.7,
            max_tokens=2048,
            google_api_key=settings.google_api_key
        )
        self.text_splitter = RecursiveCharacterTextSplitter(
            chunk_size=settings.chunk_size,
            chunk_overlap=settings.chunk_overlap
        )
        
        # Intent classification patterns
        self.greeting_patterns = [
            r'\b(hi|hello|hey|greetings|good\s+(morning|afternoon|evening))\b',
            r'\b(how\s+are\s+you|what\'?s\s+up|howdy)\b'
        ]
        
        self.general_patterns = [
            r'\b(what\s+is|tell\s+me\s+about|explain|describe)\b',
            r'\b(how\s+to|can\s+you|are\s+you\s+able)\b'
        ]
    
    async def process_message(
        self,
        chat_request: ChatRequest
    ) -> ChatMessageResponse:
        """Process incoming chat message with enhanced capabilities using ChatRequest object."""
        return await self._process_message_internal(
            message=chat_request.message,
            chatbot_id=chat_request.chatbot_id,
            user_id=chat_request.user_id,
            session_id=chat_request.session_id,
            metadata=chat_request.metadata
        )

    async def process_message_legacy(
        self,
        message: str,
        chatbot_id: uuid.UUID,
        user_id: Optional[str] = None,
        session_id: Optional[str] = None
    ) -> ChatMessageResponse:
        """Process incoming chat message with enhanced capabilities (legacy method)."""
        return await self._process_message_internal(
            message=message,
            chatbot_id=chatbot_id,
            user_id=user_id,
            session_id=session_id,
            metadata={}
        )

    async def _process_message_internal(
        self,
        message: str,
        chatbot_id: uuid.UUID,
        user_id: Optional[str] = None,
        session_id: Optional[str] = None,
        metadata: Optional[Dict[str, Any]] = None
    ) -> ChatMessageResponse:
        """Internal method to process incoming chat message with enhanced capabilities."""
        try:
            # Step 1: Intent classification
            intent = await self._classify_intent(message, chatbot_id)
            
            # Step 2: Handle based on intent
            if intent == ConversationIntent.GREETING:
                response = await self._handle_greeting(message)
                return ChatMessageResponse(
                    response=response,
                    intent=intent,
                    confidence=0.9
                )
            
            elif intent == ConversationIntent.ACTION:
                # Step 3: Function calling
                action_result = await self._handle_function_calling(message, chatbot_id)
                if action_result["executed"]:
                    response = await self._generate_action_response(message, action_result)
                    return ChatMessageResponse(
                        response=response,
                        intent=intent,
                        action_executed=action_result,
                        confidence=0.8
                    )
            
            # Step 4: RAG-based response (default)
            rag_response = await self._handle_rag_query(message, chatbot_id)
            return rag_response
            
        except Exception as e:
            logger.error(f"Error processing message: {e}")
            return ChatMessageResponse(
                response="I apologize, but I encountered an error processing your request. Please try again.",
                intent=ConversationIntent.GENERAL,
                confidence=0.0
            )
    
    async def _classify_intent(self, message: str, chatbot_id: uuid.UUID) -> ConversationIntent:
        """Classify user message intent."""
        message_lower = message.lower().strip()
        
        # Check for greetings
        for pattern in self.greeting_patterns:
            if re.search(pattern, message_lower):
                return ConversationIntent.GREETING
        
        # Check if actions are available for this chatbot
        actions = await self._get_chatbot_actions(chatbot_id)
        if actions:
            # Use LLM to determine if this is an action request
            action_check_prompt = self._create_action_classification_prompt(message, actions)
            try:
                response = await self._call_llm(action_check_prompt)
                if "ACTION_REQUIRED" in response.upper():
                    return ConversationIntent.ACTION
            except Exception as e:
                logger.error(f"Error in action classification: {e}")
        
        # Check for general patterns
        for pattern in self.general_patterns:
            if re.search(pattern, message_lower):
                return ConversationIntent.GENERAL
        
        return ConversationIntent.QUESTION
    
    async def _handle_greeting(self, message: str) -> str:
        """Handle greeting messages with natural responses."""
        greetings = [
            "Hello! How can I help you today?",
            "Hi there! What can I assist you with?",
            "Greetings! I'm here to help. What would you like to know?",
            "Hello! I'm ready to answer your questions and help with any tasks.",
            "Hi! How may I be of assistance today?"
        ]
        
        # Simple hash-based selection for consistency
        greeting_index = hash(message.lower()) % len(greetings)
        return greetings[greeting_index]
    
    async def _handle_function_calling(self, message: str, chatbot_id: uuid.UUID) -> Dict[str, Any]:
        """Handle function calling based on available actions."""
        try:
            actions = await self._get_chatbot_actions(chatbot_id)
            if not actions:
                return {"executed": False, "reason": "No actions available"}
            
            # Create function calling prompt
            function_prompt = self._create_function_calling_prompt(message, actions)
            llm_response = await self._call_llm(function_prompt)
            
            # Parse LLM response for function call
            function_call = self._parse_function_call(llm_response)
            if not function_call:
                return {"executed": False, "reason": "No function identified"}
            
            # Find matching action
            action = next((a for a in actions if a["name"] == function_call.function_name), None)
            if not action:
                return {"executed": False, "reason": f"Action '{function_call.function_name}' not found"}
            
            # Extract dynamic variables from the message
            enhanced_parameters = await self._extract_dynamic_variables(
                message, action.get("dynamic_variables", []), function_call.parameters
            )

            # Execute the action
            execution_result = await self._execute_action(action, enhanced_parameters, chatbot_id)

            return {
                "executed": True,
                "action_name": function_call.function_name,
                "parameters": enhanced_parameters,
                "result": execution_result
            }
            
        except Exception as e:
            logger.error(f"Error in function calling: {e}")
            return {"executed": False, "reason": f"Error: {str(e)}"}
    
    async def _handle_rag_query(self, message: str, chatbot_id: uuid.UUID) -> ChatMessageResponse:
        """Handle RAG-based question answering."""
        try:
            # Get relevant documents
            supabase_client = await get_supabase_client()
            vector_store = CustomSupabaseVectorStore(
                client=supabase_client,
                embedding=self.embeddings,
                table_name="documents",
                query_name="match_documents"
            )
            
            docs = vector_store.similarity_search(
                query=message,
                chatbot_id=str(chatbot_id),
                k=5
            )
            
            if not docs:
                return ChatMessageResponse(
                    response="I don't have any specific information about that topic in my knowledge base. Could you ask something else or provide more context?",
                    intent=ConversationIntent.QUESTION,
                    confidence=0.3
                )
            
            # Create RAG prompt
            context = "\n\n".join([doc.page_content for doc in docs])
            rag_prompt = self._create_rag_prompt(context, message)
            
            # Generate response
            response = await self._call_llm(rag_prompt)
            
            # Prepare sources
            sources = [
                {
                    "content": doc.page_content[:200] + "..." if len(doc.page_content) > 200 else doc.page_content,
                    "metadata": doc.metadata
                }
                for doc in docs
            ]
            
            return ChatMessageResponse(
                response=response,
                intent=ConversationIntent.QUESTION,
                sources=sources,
                confidence=0.8
            )
            
        except Exception as e:
            logger.error(f"Error in RAG query: {e}")
            return ChatMessageResponse(
                response="I encountered an error while searching my knowledge base. Please try rephrasing your question.",
                intent=ConversationIntent.QUESTION,
                confidence=0.0
            )
    
    async def _get_chatbot_actions(self, chatbot_id: uuid.UUID) -> List[Dict[str, Any]]:
        """Get available actions for a chatbot."""
        try:
            supabase_client = await get_supabase_client()
            response = supabase_client.table("actions").select("*").eq("chatbot_id", str(chatbot_id)).eq("is_active", True).execute()
            return response.data if response.data else []
        except Exception as e:
            logger.error(f"Error fetching actions: {e}")
            return []
    
    async def _execute_action(self, action: Dict[str, Any], parameters: Dict[str, Any], chatbot_id: uuid.UUID) -> FunctionCallResult:
        """Execute an action by calling its API endpoint."""
        try:
            # Apply variable substitution to endpoint URL
            url = self._substitute_variables(action["api_endpoint"], parameters)

            # Apply variable substitution to headers
            headers = action.get("headers", {})
            headers = self._substitute_variables_in_dict(headers, parameters)
            headers.setdefault("Content-Type", "application/json")

            # Apply variable substitution to request schema/body
            request_body = self._substitute_variables_in_dict(action.get("request_schema", {}), parameters)

            method = action.get("http_method", "POST").upper()
            
            # Log action execution
            log_data = {
                "action_id": action["id"],
                "chatbot_id": str(chatbot_id),
                "request_payload": parameters,
                "status": ActionStatus.PENDING
            }
            
            async with httpx.AsyncClient(timeout=30.0) as client:
                if method == "GET":
                    # For GET requests, merge parameters with any query params from request_body
                    query_params = {**parameters, **request_body}
                    response = await client.get(url, params=query_params, headers=headers)
                elif method == "POST":
                    response = await client.post(url, json=request_body, headers=headers)
                elif method == "PUT":
                    response = await client.put(url, json=request_body, headers=headers)
                elif method == "DELETE":
                    response = await client.delete(url, json=request_body if request_body else None, headers=headers)
                else:
                    raise ValueError(f"Unsupported HTTP method: {method}")
                
                response.raise_for_status()
                result_data = response.json() if response.content else {}
                
                # Update log
                log_data.update({
                    "response_payload": result_data,
                    "status": ActionStatus.SUCCESS
                })
                
                # Save log to database
                await self._save_action_log(log_data)
                
                return FunctionCallResult(success=True, result=result_data)
                
        except Exception as e:
            logger.error(f"Error executing action: {e}")
            
            # Update log with error
            log_data.update({
                "status": ActionStatus.FAILURE,
                "error_message": str(e)
            })
            await self._save_action_log(log_data)
            
            return FunctionCallResult(success=False, error=str(e))
    
    async def _save_action_log(self, log_data: Dict[str, Any]):
        """Save action execution log."""
        try:
            supabase_client = await get_supabase_client()
            log_data["executed_at"] = datetime.utcnow().isoformat()
            supabase_client.table("action_logs").insert(log_data).execute()
        except Exception as e:
            logger.error(f"Error saving action log: {e}")
    
    async def _call_llm(self, prompt: str) -> str:
        """Call the LLM with a prompt."""
        try:
            messages = [HumanMessage(content=prompt)]
            response = await self.llm.ainvoke(messages)
            return response.content
        except Exception as e:
            logger.error(f"Error calling LLM: {e}")
            raise
    
    def _create_action_classification_prompt(self, message: str, actions: List[Dict[str, Any]]) -> str:
        """Create prompt for action classification."""
        action_descriptions = "\n".join([
            f"- {action['name']}: {action['description']}"
            for action in actions
        ])
        
        return f"""You are an intelligent assistant that determines if a user message requires calling a specific action/function.

Available actions:
{action_descriptions}

User message: "{message}"

Instructions:
- If the user message clearly requests an action that matches one of the available actions, respond with "ACTION_REQUIRED"
- If the user message is a general question or doesn't require any specific action, respond with "NO_ACTION"
- Consider the intent and context of the message

Response (ACTION_REQUIRED or NO_ACTION):"""
    
    def _create_function_calling_prompt(self, message: str, actions: List[Dict[str, Any]]) -> str:
        """Create prompt for function calling."""
        action_schemas = []
        for action in actions:
            schema_str = json.dumps(action.get("request_schema", {}), indent=2)
            action_schemas.append(f"""
Action: {action['name']}
Description: {action['description']}
Parameters Schema:
{schema_str}
""")
        
        actions_text = "\n".join(action_schemas)
        
        return f"""You are a function calling assistant. Based on the user's message, determine which function to call and extract the required parameters.

Available functions:
{actions_text}

User message: "{message}"

Instructions:
1. Identify which function best matches the user's request
2. Extract the required parameters from the message
3. Respond in this exact JSON format:
{{
    "function_name": "exact_function_name",
    "parameters": {{
        "param1": "value1",
        "param2": "value2"
    }}
}}

If no function is needed, respond with:
{{"function_name": null, "parameters": {{}}}}

JSON Response:"""
    
    def _create_rag_prompt(self, context: str, question: str) -> str:
        """Create prompt for RAG-based answering."""
        return f"""Answer the question based on the provided context. Be helpful, accurate, and conversational.

Context:
{context}

Question: {question}

Instructions:
- Provide a detailed and helpful answer based on the context
- If the answer isn't in the context, say "I don't have that specific information in my knowledge base"
- Be conversational and natural in your response
- Don't mention "the context" or "the documents" - just answer naturally

Answer:"""
    
    def _parse_function_call(self, llm_response: str) -> Optional[FunctionCall]:
        """Parse LLM response for function call."""
        try:
            # Extract JSON from response
            json_match = re.search(r'\{.*\}', llm_response, re.DOTALL)
            if not json_match:
                return None
            
            function_data = json.loads(json_match.group())
            
            if not function_data.get("function_name"):
                return None
            
            return FunctionCall(
                function_name=function_data["function_name"],
                parameters=function_data.get("parameters", {})
            )
            
        except Exception as e:
            logger.error(f"Error parsing function call: {e}")
            return None
    
    async def _extract_dynamic_variables(
        self,
        message: str,
        dynamic_variables: List[Dict[str, Any]],
        existing_parameters: Dict[str, Any]
    ) -> Dict[str, Any]:
        """Extract dynamic variables from user message using AI."""
        if not dynamic_variables:
            return existing_parameters

        try:
            # Create extraction prompt
            variables_info = []
            for var in dynamic_variables:
                variables_info.append(f"""
Variable: {var['name']}
Type: {var['variable_type']}
Description: {var['description']}
Required: {var.get('required', True)}
Default: {var.get('default_value', 'None')}""")

            variables_text = "\n".join(variables_info)

            extraction_prompt = f"""You are an AI assistant that extracts specific information from user messages.

User message: "{message}"

Extract the following variables from the message:
{variables_text}

Instructions:
1. Extract each variable value from the user message
2. Use the variable type to guide extraction (e.g., ip_address should be a valid IP, city should be a city name)
3. If a variable is not found and has a default value, use the default
4. If a required variable is not found and has no default, set it to null
5. Return the result in JSON format

Example response:
{{
    "ip_address": "***********",
    "city": "New York",
    "user_email": "<EMAIL>"
}}

JSON Response:"""

            # Call LLM to extract variables
            llm_response = await self._call_llm(extraction_prompt)

            # Parse the response
            json_match = re.search(r'\{.*\}', llm_response, re.DOTALL)
            if json_match:
                extracted_vars = json.loads(json_match.group())

                # Merge with existing parameters
                enhanced_parameters = existing_parameters.copy()
                enhanced_parameters.update(extracted_vars)

                return enhanced_parameters

        except Exception as e:
            logger.error(f"Error extracting dynamic variables: {e}")

        return existing_parameters

    def _substitute_variables(self, template: str, variables: Dict[str, Any]) -> str:
        """Substitute variables in a template string using {{variable_name}} syntax."""
        if not template or not isinstance(template, str):
            return template

        result = template
        for key, value in variables.items():
            # Convert value to string, handling None values
            str_value = str(value) if value is not None else ""
            # Replace {{key}} with the value
            result = result.replace(f"{{{{{key}}}}}", str_value)

        return result

    def _substitute_variables_in_dict(self, data: Dict[str, Any], variables: Dict[str, Any]) -> Dict[str, Any]:
        """Recursively substitute variables in a dictionary."""
        if not isinstance(data, dict):
            return data

        result = {}
        for key, value in data.items():
            if isinstance(value, str):
                # Substitute variables in string values
                result[key] = self._substitute_variables(value, variables)
            elif isinstance(value, dict):
                # Recursively process nested dictionaries
                result[key] = self._substitute_variables_in_dict(value, variables)
            elif isinstance(value, list):
                # Process lists
                result[key] = self._substitute_variables_in_list(value, variables)
            else:
                # Keep other types as-is
                result[key] = value

        return result

    def _substitute_variables_in_list(self, data: List[Any], variables: Dict[str, Any]) -> List[Any]:
        """Recursively substitute variables in a list."""
        if not isinstance(data, list):
            return data

        result = []
        for item in data:
            if isinstance(item, str):
                result.append(self._substitute_variables(item, variables))
            elif isinstance(item, dict):
                result.append(self._substitute_variables_in_dict(item, variables))
            elif isinstance(item, list):
                result.append(self._substitute_variables_in_list(item, variables))
            else:
                result.append(item)

        return result

    async def _generate_action_response(self, original_message: str, action_result: Dict[str, Any]) -> str:
        """Generate natural response after action execution using AI to interpret API response."""
        result = action_result["result"]  # This is a FunctionCallResult object
        action_name = action_result["action_name"]

        if result.success:
            result_data = result.result or {}

            # Use AI to generate a natural response based on the API response data
            try:
                response_prompt = f"""You are an AI assistant that interprets API response data and creates natural, informative responses for users.

User's original request: "{original_message}"
Action executed: {action_name}
API Response Data: {json.dumps(result_data, indent=2)}

Your task:
1. Analyze the API response data and extract the most relevant information
2. Create a natural, conversational response that:
   - Confirms the action was completed successfully
   - Presents the key information from the API response in a user-friendly way
   - Relates back to what the user originally asked for
   - Is concise but informative

Guidelines:
- If the response contains specific data (like IP addresses, locations, weather, etc.), include those details
- Format any technical data in a readable way
- Don't just say "action completed" - provide the actual results
- Keep the tone helpful and conversational
- If there's a lot of data, summarize the most important parts

Response:"""

                # Generate response using LLM
                ai_response = await self._call_llm(response_prompt)

                # Clean up the response (remove any potential formatting artifacts)
                ai_response = ai_response.strip()

                # Fallback to basic response if AI response is too short or seems invalid
                if len(ai_response) < 20 or not ai_response:
                    message = result_data.get('message', 'The action was completed.') if isinstance(result_data, dict) else 'The action was completed.'
                    return f"I've successfully executed the {action_name} action for you. {message}"

                return ai_response

            except Exception as e:
                logger.error(f"Error generating AI response: {e}")
                # Fallback to original logic if AI generation fails
                message = result_data.get('message', 'The action was completed.') if isinstance(result_data, dict) else 'The action was completed.'
                return f"I've successfully executed the {action_name} action for you. {message}"
        else:
            return f"I attempted to execute the {action_name} action, but encountered an error: {result.error}"
    
    async def add_documents(self, text_chunks: List[str], chatbot_id: uuid.UUID, metadata: Optional[Dict[str, Any]] = None) -> int:
        """Add document chunks to the knowledge base."""
        try:
            supabase_client = await get_supabase_client()

            # Create documents with metadata (but not chatbot_id in metadata)
            docs_with_metadata = [
                Document(
                    page_content=chunk,
                    metadata=metadata or {}
                )
                for chunk in text_chunks
            ]

            # Create embeddings for all documents
            embeddings_list = self.embeddings.embed_documents([doc.page_content for doc in docs_with_metadata])

            # Prepare data for direct insertion into Supabase
            documents_data = []
            for i, (doc, embedding) in enumerate(zip(docs_with_metadata, embeddings_list)):
                doc_data = {
                    "content": doc.page_content,
                    "metadata": doc.metadata,
                    "embedding": embedding,
                    "chatbot_id": str(chatbot_id)  # Explicitly set chatbot_id column
                }
                documents_data.append(doc_data)

            # Insert documents directly into Supabase
            result = supabase_client.table("documents").insert(documents_data).execute()

            if not result.data:
                raise Exception("Failed to insert documents")

            return len(text_chunks)

        except Exception as e:
            logger.error(f"Error adding documents: {e}")
            raise
    
    async def search_knowledge_base(self, query: str, chatbot_id: uuid.UUID, k: int = 5) -> List[SearchResult]:
        """Search the knowledge base."""
        try:
            supabase_client = await get_supabase_client()
            vector_store = CustomSupabaseVectorStore(
                client=supabase_client,
                embedding=self.embeddings,
                table_name="documents",
                query_name="match_documents"
            )
            
            docs = vector_store.similarity_search(
                query=query,
                chatbot_id=str(chatbot_id),
                k=k
            )
            
            return [
                SearchResult(
                    content=doc.page_content,
                    metadata=doc.metadata,
                    score=0.8  # Placeholder score
                )
                for doc in docs
            ]
            
        except Exception as e:
            logger.error(f"Error searching knowledge base: {e}")
            return []


# Global chat service instance
chat_service = ChatService()


async def get_chat_service() -> ChatService:
    """Dependency to get chat service."""
    return chat_service
