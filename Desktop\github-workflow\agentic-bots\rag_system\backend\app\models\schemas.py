"""
Pydantic schemas for request/response validation.
"""
from typing import List, Optional, Dict, Any
from pydantic import BaseModel, Field
from datetime import datetime
from enum import Enum
import uuid


# Base Schemas
class BaseSchema(BaseModel):
    """Base schema with common configuration."""
    
    class Config:
        from_attributes = True
        validate_assignment = True
        use_enum_values = True


# Enums
class ActionStatus(str, Enum):
    SUCCESS = "success"
    FAILURE = "failure"
    PENDING = "pending"


class ConversationIntent(str, Enum):
    GREETING = "greeting"
    QUESTION = "question"
    ACTION = "action"
    GENERAL = "general"


# Chatbot Schemas
class ChatbotBase(BaseSchema):
    name: str = Field(..., min_length=1, max_length=100)
    description: Optional[str] = Field(None, max_length=500)
    is_active: bool = Field(default=True)


class ChatbotCreate(ChatbotBase):
    pass


class ChatbotUpdate(BaseSchema):
    name: Optional[str] = Field(None, min_length=1, max_length=100)
    description: Optional[str] = Field(None, max_length=500)
    is_active: Optional[bool] = None


class Chatbot(ChatbotBase):
    id: uuid.UUID
    created_at: datetime
    updated_at: datetime


# Document Schemas
class DocumentBase(BaseSchema):
    content: str = Field(..., min_length=1)
    metadata: Optional[Dict[str, Any]] = Field(default_factory=dict)


class DocumentCreate(DocumentBase):
    chatbot_id: uuid.UUID


class Document(DocumentBase):
    id: uuid.UUID
    chatbot_id: uuid.UUID
    created_at: datetime


# Dynamic Variable Schemas
class DynamicVariable(BaseSchema):
    name: str = Field(..., min_length=1, max_length=50)
    description: str = Field(..., min_length=1, max_length=200)
    variable_type: str = Field(..., pattern="^(string|number|boolean|email|ip_address|city|country|date|url)$")
    extraction_pattern: Optional[str] = Field(None, max_length=500)
    default_value: Optional[str] = None
    required: bool = Field(default=True)

# Action Schemas
class ActionBase(BaseSchema):
    name: str = Field(..., min_length=1, max_length=100)
    description: str = Field(..., min_length=1, max_length=500)
    api_endpoint: str = Field(..., min_length=1)
    http_method: str = Field(default="POST", pattern="^(GET|POST|PUT|DELETE|PATCH)$")
    request_schema: Dict[str, Any] = Field(default_factory=dict)
    headers: Optional[Dict[str, str]] = Field(default_factory=dict)
    dynamic_variables: List[DynamicVariable] = Field(default_factory=list)
    is_active: bool = Field(default=True)


class ActionCreate(ActionBase):
    chatbot_id: uuid.UUID


class ActionUpdate(BaseSchema):
    name: Optional[str] = Field(None, min_length=1, max_length=100)
    description: Optional[str] = Field(None, min_length=1, max_length=500)
    api_endpoint: Optional[str] = Field(None, min_length=1)
    http_method: Optional[str] = Field(None, pattern="^(GET|POST|PUT|DELETE|PATCH)$")
    request_schema: Optional[Dict[str, Any]] = None
    headers: Optional[Dict[str, str]] = None
    dynamic_variables: Optional[List[DynamicVariable]] = None
    is_active: Optional[bool] = None


class Action(ActionBase):
    id: uuid.UUID
    chatbot_id: uuid.UUID
    created_at: datetime
    updated_at: datetime


# Action Log Schemas
class ActionLogBase(BaseSchema):
    request_payload: Dict[str, Any] = Field(default_factory=dict)
    response_payload: Optional[Dict[str, Any]] = Field(default_factory=dict)
    status: ActionStatus
    error_message: Optional[str] = None


class ActionLogCreate(ActionLogBase):
    action_id: uuid.UUID
    chatbot_id: uuid.UUID


class ActionLog(ActionLogBase):
    id: uuid.UUID
    action_id: uuid.UUID
    chatbot_id: uuid.UUID
    executed_at: datetime


# Email Template Schemas
class EmailTemplateBase(BaseSchema):
    name: str = Field(..., min_length=1, max_length=100)
    subject: str = Field(..., min_length=1, max_length=200)
    body: str = Field(..., min_length=1)
    is_active: bool = Field(default=True)


class EmailTemplateCreate(EmailTemplateBase):
    chatbot_id: uuid.UUID


class EmailTemplateUpdate(BaseSchema):
    name: Optional[str] = Field(None, min_length=1, max_length=100)
    subject: Optional[str] = Field(None, min_length=1, max_length=200)
    body: Optional[str] = Field(None, min_length=1)
    is_active: Optional[bool] = None


class EmailTemplate(EmailTemplateBase):
    id: uuid.UUID
    chatbot_id: uuid.UUID
    created_at: datetime
    updated_at: datetime


# Chat Widget Schemas
class ChatWidgetBase(BaseSchema):
    allowed_domains: List[str] = Field(default_factory=list)
    widget_config: Dict[str, Any] = Field(default_factory=dict)
    is_active: bool = Field(default=True)


class ChatWidgetCreateRequest(ChatWidgetBase):
    """Schema for widget creation request body (without chatbot_id)"""
    pass


class ChatWidgetCreate(ChatWidgetBase):
    """Schema for widget creation with chatbot_id (used internally)"""
    chatbot_id: uuid.UUID


class ChatWidgetUpdate(BaseSchema):
    allowed_domains: Optional[List[str]] = None
    widget_config: Optional[Dict[str, Any]] = None
    is_active: Optional[bool] = None


class ChatWidget(ChatWidgetBase):
    id: uuid.UUID
    chatbot_id: uuid.UUID
    api_key: str
    created_at: datetime
    updated_at: datetime


# Chat Schemas
class ChatMessageBase(BaseSchema):
    message: str = Field(..., min_length=1, max_length=4000)
    user_id: Optional[str] = Field(None, max_length=100)
    session_id: Optional[str] = Field(None, max_length=100)


class ChatMessageCreate(ChatMessageBase):
    pass


class ChatRequest(BaseSchema):
    """Schema for chat requests with full context."""
    chatbot_id: uuid.UUID
    message: str = Field(..., min_length=1, max_length=4000)
    user_id: Optional[str] = Field(None, max_length=100)
    session_id: Optional[str] = Field(None, max_length=100)
    metadata: Optional[Dict[str, Any]] = Field(default_factory=dict)


class ChatMessageResponse(BaseSchema):
    response: str
    intent: ConversationIntent
    action_executed: Optional[Dict[str, Any]] = None
    sources: List[Dict[str, Any]] = Field(default_factory=list)
    confidence: float = Field(default=0.0, ge=0.0, le=1.0)


# File Upload Schemas
class FileUploadResponse(BaseSchema):
    filename: str
    size: int
    chunks_created: int
    message: str


# Search Schemas
class SearchQuery(BaseSchema):
    query: str = Field(..., min_length=1, max_length=500)
    k: int = Field(default=5, ge=1, le=20)
    threshold: float = Field(default=0.7, ge=0.0, le=1.0)


class SearchResult(BaseSchema):
    content: str
    metadata: Dict[str, Any]
    score: float


# Function Calling Schemas
class FunctionCall(BaseSchema):
    function_name: str
    parameters: Dict[str, Any] = Field(default_factory=dict)


class FunctionCallResult(BaseSchema):
    success: bool
    result: Optional[Dict[str, Any]] = None
    error: Optional[str] = None


# API Response Schemas
class APIResponse(BaseSchema):
    success: bool
    message: str
    data: Optional[Any] = None


class PaginatedResponse(BaseSchema):
    items: List[Any]
    total: int
    page: int
    per_page: int
    pages: int


# Widget Embed Schema
class WidgetEmbed(BaseSchema):
    chatbot_id: uuid.UUID
    domain: str
    config: Optional[Dict[str, Any]] = Field(default_factory=dict)
