"""
Chat widgets API endpoints for embeddable widgets.
"""
from typing import Dict, Any, Optional
import uuid
from fastapi import <PERSON><PERSON><PERSON><PERSON>, Depends, HTTPEx<PERSON>, Request, Header
from fastapi.responses import Response, HTMLResponse
from pydantic import BaseModel

from app.models.schemas import (
    ChatWidget, ChatWidgetCreate, ChatWidgetCreateRequest, ChatWidgetUpdate,
    PaginatedResponse, APIResponse, ChatRequest
)
from app.services.widget_service import WidgetService, get_widget_service
from app.services.chat_service import get_chat_service, ChatService
import structlog

logger = structlog.get_logger()

# Authenticated router for managing widgets within your application
router = APIRouter(prefix="/chatbots/{chatbot_id}/widgets", tags=["widgets"])

# Public router for widget operation (script serving, chat) that will be accessed by end-users' browsers
public_router = APIRouter(prefix="/widgets", tags=["public-widgets"])


# --- Widget Management Endpoints (for your app's frontend/dashboard) ---

@router.post("/", response_model=ChatWidget)
async def create_widget(
    chatbot_id: uuid.UUID,
    widget_data: ChatWidgetCreateRequest,
    widget_service: WidgetService = Depends(get_widget_service)
):
    """Create a new chat widget for a chatbot."""
    try:
        # Create the full widget data with chatbot_id
        full_widget_data = ChatWidgetCreate(
            chatbot_id=chatbot_id,
            allowed_domains=widget_data.allowed_domains,
            widget_config=widget_data.widget_config,
            is_active=widget_data.is_active
        )

        # Set default config if not provided
        if not full_widget_data.widget_config:
            full_widget_data.widget_config = widget_service.get_default_widget_config()

        widget = await widget_service.create_widget(full_widget_data)
        return widget

    except Exception as e:
        logger.error(f"Error creating widget: {e}")
        raise HTTPException(status_code=500, detail="Error creating widget")


@router.get("/", response_model=PaginatedResponse)
async def get_chatbot_widgets(
    chatbot_id: uuid.UUID,
    skip: int = 0,
    limit: int = 10,
    active_only: bool = True,
    widget_service: WidgetService = Depends(get_widget_service)
):
    """Get all widgets for a chatbot with pagination."""
    try:
        result = await widget_service.get_chatbot_widgets(
            chatbot_id=chatbot_id,
            skip=skip,
            limit=limit,
            active_only=active_only
        )
        return result
    except Exception as e:
        logger.error(f"Error getting chatbot widgets: {e}")
        raise HTTPException(status_code=500, detail="Error retrieving widgets")


@router.get("/{widget_id}", response_model=ChatWidget)
async def get_widget(
    chatbot_id: uuid.UUID,
    widget_id: uuid.UUID,
    widget_service: WidgetService = Depends(get_widget_service)
):
    """Get a specific widget by ID."""
    try:
        widget = await widget_service.get_widget(widget_id, chatbot_id)
        if not widget:
            raise HTTPException(status_code=404, detail="Widget not found")
        return widget
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error getting widget: {e}")
        raise HTTPException(status_code=500, detail="Error retrieving widget")


@router.put("/{widget_id}", response_model=ChatWidget)
async def update_widget(
    chatbot_id: uuid.UUID,
    widget_id: uuid.UUID,
    widget_data: ChatWidgetUpdate,
    widget_service: WidgetService = Depends(get_widget_service)
):
    """Update an existing widget."""
    try:
        widget = await widget_service.update_widget(widget_id, chatbot_id, widget_data)
        if not widget:
            raise HTTPException(status_code=404, detail="Widget not found")
        return widget
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error updating widget: {e}")
        raise HTTPException(status_code=500, detail="Error updating widget")


@router.delete("/{widget_id}")
async def delete_widget(
    chatbot_id: uuid.UUID,
    widget_id: uuid.UUID,
    widget_service: WidgetService = Depends(get_widget_service)
):
    """Delete a widget (soft delete)."""
    try:
        success = await widget_service.delete_widget(widget_id, chatbot_id)
        if not success:
            raise HTTPException(status_code=404, detail="Widget not found")
        
        return APIResponse(
            success=True,
            message="Widget deleted successfully"
        )
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error deleting widget: {e}")
        raise HTTPException(status_code=500, detail="Error deleting widget")


@router.post("/{widget_id}/regenerate_key", response_model=ChatWidget)
async def regenerate_api_key(
    chatbot_id: uuid.UUID,
    widget_id: uuid.UUID,
    widget_service: WidgetService = Depends(get_widget_service)
):
    """Regenerate API key for a widget."""
    try:
        widget = await widget_service.regenerate_api_key(widget_id, chatbot_id)
        if not widget:
            raise HTTPException(status_code=404, detail="Widget not found")
        return widget
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error regenerating API key: {e}")
        raise HTTPException(status_code=500, detail="Error regenerating API key")


@router.get("/{widget_id}/embed_code")
async def get_widget_embed_code(
    chatbot_id: uuid.UUID,
    widget_id: uuid.UUID,
    widget_service: WidgetService = Depends(get_widget_service)
):
    """Generate embed code for the widget."""
    try:
        embed_codes = await widget_service.get_widget_embed_code(widget_id, chatbot_id)
        
        return APIResponse(
            success=True,
            message="Embed code generated successfully",
            data=embed_codes
        )
        
    except ValueError as e:
        raise HTTPException(status_code=404, detail=str(e))
    except Exception as e:
        logger.error(f"Error generating embed code: {e}")
        raise HTTPException(status_code=500, detail="Error generating embed code")


@router.get("/{widget_id}/analytics")
async def get_widget_analytics(
    chatbot_id: uuid.UUID,
    widget_id: uuid.UUID,
    days: int = 30,
    widget_service: WidgetService = Depends(get_widget_service)
):
    """Get analytics data for a widget."""
    try:
        analytics = await widget_service.get_widget_analytics(widget_id, chatbot_id, days)
        
        return APIResponse(
            success=True,
            message="Analytics data retrieved successfully",
            data=analytics
        )
        
    except Exception as e:
        logger.error(f"Error getting widget analytics: {e}")
        raise HTTPException(status_code=500, detail="Error retrieving analytics")


@router.post("/{widget_id}/customize")
async def customize_widget_appearance(
    chatbot_id: uuid.UUID,
    widget_id: uuid.UUID,
    appearance_config: Dict[str, Any],
    widget_service: WidgetService = Depends(get_widget_service)
):
    """Update widget appearance configuration."""
    try:
        widget = await widget_service.customize_widget_appearance(
            widget_id, chatbot_id, appearance_config
        )
        
        if not widget:
            raise HTTPException(status_code=404, detail="Widget not found")
        
        return widget
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error customizing widget: {e}")
        raise HTTPException(status_code=500, detail="Error customizing widget")


@router.get("/config/default")
async def get_default_widget_config(
    chatbot_id: uuid.UUID, # chatbot_id is unused but required for router path
    widget_service: WidgetService = Depends(get_widget_service)
):
    """Get default widget configuration."""
    try:
        config = widget_service.get_default_widget_config()
        
        return APIResponse(
            success=True,
            message="Default widget configuration",
            data=config
        )
        
    except Exception as e:
        logger.error(f"Error getting default config: {e}")
        raise HTTPException(status_code=500, detail="Error getting default configuration")

# --- Public Widget Endpoints (for the script to call) ---

class WidgetChatMessage(BaseModel):
    message: str
    session_id: str

# Add OPTIONS handler for CORS preflight requests
@public_router.options("/chat")
async def options_widget_chat():
    """Handle CORS preflight requests for widget chat."""
    return Response(
        content="",
        headers={
            "Access-Control-Allow-Origin": "*",
            "Access-Control-Allow-Methods": "POST, OPTIONS",
            "Access-Control-Allow-Headers": "Content-Type, Authorization, Origin",
            "Access-Control-Max-Age": "86400"
        }
    )

@public_router.post("/chat")
async def public_widget_chat(
    request: Request,
    chat_data: WidgetChatMessage,
    authorization: Optional[str] = Header(None),
    widget_service: WidgetService = Depends(get_widget_service),
    chat_service: ChatService = Depends(get_chat_service)
):
    """Handles chat messages coming from an embedded widget."""
    try:
        if not authorization or not authorization.startswith("Bearer "):
            raise HTTPException(status_code=401, detail="Unauthorized: Invalid token format")

        api_key = authorization.split("Bearer ")[1]
        origin = request.headers.get("origin")

        is_allowed, widget = await widget_service.validate_widget_access(api_key, origin)

        if not is_allowed or not widget:
            logger.warning(
                "Forbidden widget access",
                api_key=api_key[:12] + "...",
                origin=origin,
                allowed_domains=widget.allowed_domains if widget else "N/A"
            )
            raise HTTPException(status_code=403, detail="Forbidden: This domain is not permitted to use the widget.")

        chat_request = ChatRequest(
            chatbot_id=widget.chatbot_id,
            message=chat_data.message,
            session_id=chat_data.session_id,
            user_id=f"widget_user_{widget.id}",
            metadata={"source": "widget", "origin": origin}
        )
        
        response_data = await chat_service.process_message(chat_request)

        return Response(
            content=f'{{"response": "{response_data.response}"}}',
            media_type="application/json",
            headers={
                "Access-Control-Allow-Origin": "*",
                "Access-Control-Allow-Methods": "POST, OPTIONS",
                "Access-Control-Allow-Headers": "Content-Type, Authorization, Origin"
            }
        )

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error processing widget chat: {e}", exc_info=True)
        return Response(
            content='{"response": "Sorry, I\'m having trouble connecting right now. Please try again later."}',
            media_type="application/json",
            headers={
                "Access-Control-Allow-Origin": "*",
                "Access-Control-Allow-Methods": "POST, OPTIONS",
                "Access-Control-Allow-Headers": "Content-Type, Authorization, Origin"
            }
        )


@public_router.get("/script.js")
async def get_widget_script_file(
    key: str,
    widget_service: WidgetService = Depends(get_widget_service)
):
    """Serves the dynamic, self-contained JavaScript file for the chat widget."""
    try:
        script_content = await widget_service.generate_widget_script(key)

        if not script_content:
            return Response(
                content="console.error('AI Chat Widget: Invalid API key or widget is inactive.');",
                media_type="application/javascript",
                status_code=404,
                headers={
                    "Access-Control-Allow-Origin": "*",
                    "Cache-Control": "no-cache"
                }
            )

        return Response(
            content=script_content,
            media_type="application/javascript",
            headers={
                "Cache-Control": "public, max-age=3600",
                "Access-Control-Allow-Origin": "*"
            }
        )
    except Exception as e:
        logger.error(f"Error generating widget script: {e}")
        error_script = "console.error('AI Chat Widget: Failed to load script. Please check the API key and server status.');"
        return Response(
            content=error_script,
            media_type="application/javascript",
            status_code=500,
            headers={"Access-Control-Allow-Origin": "*"}
        )


# Widget router for serving the actual widget HTML page
widget_router = APIRouter(prefix="/widget", tags=["widget-page"])

@widget_router.get("/{api_key}")
async def get_widget_page(
    api_key: str,
    widget_service: WidgetService = Depends(get_widget_service)
):
    """Serves the HTML page for the chat widget."""
    try:
        # Validate the API key and get widget configuration
        widget = await widget_service.get_widget_by_api_key(api_key)

        if not widget or not widget.is_active:
            return HTMLResponse(
                content="""
                <!DOCTYPE html>
                <html>
                <head>
                    <title>Widget Not Found</title>
                    <meta charset="utf-8">
                    <meta name="viewport" content="width=device-width, initial-scale=1">
                </head>
                <body style="font-family: Arial, sans-serif; text-align: center; padding: 50px;">
                    <h2>Widget Not Available</h2>
                    <p>This chat widget is not available or has been deactivated.</p>
                </body>
                </html>
                """,
                status_code=404
            )

        # Get widget configuration
        config = widget.widget_config or {}
        appearance = config.get("appearance", {})
        behavior = config.get("behavior", {})

        # Generate the widget HTML
        widget_html = f"""
        <!DOCTYPE html>
        <html lang="en">
        <head>
            <meta charset="UTF-8">
            <meta name="viewport" content="width=device-width, initial-scale=1.0">
            <title>AI Chat Widget</title>
            <style>
                * {{
                    margin: 0;
                    padding: 0;
                    box-sizing: border-box;
                }}

                body {{
                    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
                    background: {appearance.get('theme', 'light') == 'dark' and '#1a1a1a' or '#ffffff'};
                    color: {appearance.get('theme', 'light') == 'dark' and '#ffffff' or '#333333'};
                    height: 100vh;
                    display: flex;
                    flex-direction: column;
                }}

                .chat-container {{
                    display: flex;
                    flex-direction: column;
                    height: 100vh;
                    max-width: 100%;
                }}

                .chat-header {{
                    background: {appearance.get('primaryColor', '#007bff')};
                    color: white;
                    padding: 15px 20px;
                    text-align: center;
                    font-weight: 600;
                    font-size: 16px;
                }}

                .chat-messages {{
                    flex: 1;
                    overflow-y: auto;
                    padding: 20px;
                    background: {appearance.get('theme', 'light') == 'dark' and '#2a2a2a' or '#f8f9fa'};
                }}

                .message {{
                    margin-bottom: 15px;
                    display: flex;
                    align-items: flex-start;
                }}

                .message.user {{
                    justify-content: flex-end;
                }}

                .message-content {{
                    max-width: 80%;
                    padding: 12px 16px;
                    border-radius: 18px;
                    word-wrap: break-word;
                }}

                .message.bot .message-content {{
                    background: {appearance.get('theme', 'light') == 'dark' and '#404040' or '#e9ecef'};
                    color: {appearance.get('theme', 'light') == 'dark' and '#ffffff' or '#333333'};
                }}

                .message.user .message-content {{
                    background: {appearance.get('primaryColor', '#007bff')};
                    color: white;
                }}

                .chat-input-container {{
                    padding: 20px;
                    background: {appearance.get('theme', 'light') == 'dark' and '#1a1a1a' or '#ffffff'};
                    border-top: 1px solid {appearance.get('theme', 'light') == 'dark' and '#404040' or '#e9ecef'};
                }}

                .chat-input-form {{
                    display: flex;
                    gap: 10px;
                }}

                .chat-input {{
                    flex: 1;
                    padding: 12px 16px;
                    border: 1px solid {appearance.get('theme', 'light') == 'dark' and '#404040' or '#ddd'};
                    border-radius: 25px;
                    outline: none;
                    font-size: 14px;
                    background: {appearance.get('theme', 'light') == 'dark' and '#2a2a2a' or '#ffffff'};
                    color: {appearance.get('theme', 'light') == 'dark' and '#ffffff' or '#333333'};
                }}

                .chat-input:focus {{
                    border-color: {appearance.get('primaryColor', '#007bff')};
                }}

                .send-button {{
                    padding: 12px 20px;
                    background: {appearance.get('primaryColor', '#007bff')};
                    color: white;
                    border: none;
                    border-radius: 25px;
                    cursor: pointer;
                    font-size: 14px;
                    font-weight: 600;
                    transition: background-color 0.2s;
                }}

                .send-button:hover {{
                    opacity: 0.9;
                }}

                .send-button:disabled {{
                    opacity: 0.6;
                    cursor: not-allowed;
                }}

                .typing-indicator {{
                    display: none;
                    padding: 10px 20px;
                    font-style: italic;
                    color: #666;
                }}

                .welcome-message {{
                    text-align: center;
                    padding: 20px;
                    color: #666;
                    font-style: italic;
                }}
            </style>
        </head>
        <body>
            <div class="chat-container">
                <div class="chat-header">
                    AI Assistant
                </div>

                <div class="chat-messages" id="chatMessages">
                    <div class="welcome-message">
                        {behavior.get('welcomeMessage', 'Hello! How can I help you today?')}
                    </div>
                </div>

                <div class="typing-indicator" id="typingIndicator">
                    AI is typing...
                </div>

                <div class="chat-input-container">
                    <form class="chat-input-form" id="chatForm">
                        <input
                            type="text"
                            class="chat-input"
                            id="messageInput"
                            placeholder="Type your message..."
                            autocomplete="off"
                        >
                        <button type="submit" class="send-button" id="sendButton">
                            Send
                        </button>
                    </form>
                </div>
            </div>

            <script>
                class ChatWidget {{
                    constructor() {{
                        this.apiKey = '{api_key}';
                        this.sessionId = this.generateSessionId();
                        this.messagesContainer = document.getElementById('chatMessages');
                        this.messageInput = document.getElementById('messageInput');
                        this.sendButton = document.getElementById('sendButton');
                        this.chatForm = document.getElementById('chatForm');
                        this.typingIndicator = document.getElementById('typingIndicator');

                        this.initializeEventListeners();
                    }}

                    generateSessionId() {{
                        return 'session_' + Math.random().toString(36).substr(2, 9) + '_' + Date.now();
                    }}

                    initializeEventListeners() {{
                        this.chatForm.addEventListener('submit', (e) => {{
                            e.preventDefault();
                            this.sendMessage();
                        }});

                        this.messageInput.addEventListener('keypress', (e) => {{
                            if (e.key === 'Enter' && !e.shiftKey) {{
                                e.preventDefault();
                                this.sendMessage();
                            }}
                        }});
                    }}

                    async sendMessage() {{
                        const message = this.messageInput.value.trim();
                        if (!message) return;

                        // Add user message to chat
                        this.addMessage(message, 'user');
                        this.messageInput.value = '';
                        this.setLoading(true);

                        try {{
                            const response = await fetch('/api/v1/widgets/chat', {{
                                method: 'POST',
                                headers: {{
                                    'Content-Type': 'application/json',
                                    'Authorization': `Bearer ${{this.apiKey}}`
                                }},
                                body: JSON.stringify({{
                                    message: message,
                                    session_id: this.sessionId
                                }})
                            }});

                            if (!response.ok) {{
                                throw new Error(`HTTP error! status: ${{response.status}}`);
                            }}

                            const data = await response.json();
                            this.addMessage(data.response, 'bot');

                        }} catch (error) {{
                            console.error('Error sending message:', error);
                            this.addMessage('Sorry, I encountered an error. Please try again.', 'bot');
                        }} finally {{
                            this.setLoading(false);
                        }}
                    }}

                    addMessage(content, sender) {{
                        const messageDiv = document.createElement('div');
                        messageDiv.className = `message ${{sender}}`;

                        const contentDiv = document.createElement('div');
                        contentDiv.className = 'message-content';
                        contentDiv.textContent = content;

                        messageDiv.appendChild(contentDiv);
                        this.messagesContainer.appendChild(messageDiv);

                        // Remove welcome message if it exists
                        const welcomeMessage = this.messagesContainer.querySelector('.welcome-message');
                        if (welcomeMessage) {{
                            welcomeMessage.remove();
                        }}

                        // Scroll to bottom
                        this.messagesContainer.scrollTop = this.messagesContainer.scrollHeight;
                    }}

                    setLoading(loading) {{
                        this.sendButton.disabled = loading;
                        this.messageInput.disabled = loading;

                        if (loading) {{
                            this.typingIndicator.style.display = 'block';
                            this.sendButton.textContent = 'Sending...';
                        }} else {{
                            this.typingIndicator.style.display = 'none';
                            this.sendButton.textContent = 'Send';
                        }}
                    }}
                }}

                // Initialize the chat widget when the page loads
                document.addEventListener('DOMContentLoaded', () => {{
                    new ChatWidget();
                }});
            </script>
        </body>
        </html>
        """

        return HTMLResponse(content=widget_html)

    except Exception as e:
        logger.error(f"Error serving widget page: {e}")
        return HTMLResponse(
            content="""
            <!DOCTYPE html>
            <html>
            <head>
                <title>Widget Error</title>
                <meta charset="utf-8">
                <meta name="viewport" content="width=device-width, initial-scale=1">
            </head>
            <body style="font-family: Arial, sans-serif; text-align: center; padding: 50px;">
                <h2>Widget Error</h2>
                <p>There was an error loading the chat widget. Please try again later.</p>
            </body>
            </html>
            """,
            status_code=500
        )