# Enhanced RAG System Environment Configuration
# Development configuration - replace with actual values

# API Configuration
DEBUG=true
HOST=0.0.0.0
PORT=8000
CORS_ORIGINS=http://localhost:3000,http://localhost:5173,http://localhost:8080,http://localhost:80

# Supabase Configuration (replace with your actual values)
SUPABASE_URL=https://tptxibitoirvgrqybolb.supabase.co
SUPABASE_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InRwdHhpYml0b2lydmdycXlib2xiIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTA0MjIzMTksImV4cCI6MjA2NTk5ODMxOX0.VR2EelubHavncKxyKGc8D4iTsIe7Zns_Sp4dC5raZUE
SUPABASE_SERVICE_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InRwdHhpYml0b2lydmdycXlib2xiIiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTc1MDQyMjMxOSwiZXhwIjoyMDY1OTk4MzE5fQ.64B54HEPC_qzu_qXKT5KplE0fLY1vdK7e2Tcjw-3A6M

# Google Gemini Configuration (replace with your actual API key)
GOOGLE_API_KEY=AIzaSyCKH3YkE1Nw9CrFKFtyB7qMlikwQ0TKjkU
GEMINI_MODEL=gemini-2.5-flash-lite-preview-06-17
EMBEDDING_MODEL=models/embedding-001

# Security Configuration
SECRET_KEY=your_secret_key_here_generate_a_strong_one_at_least_32_characters
ALGORITHM=HS256
ACCESS_TOKEN_EXPIRE_MINUTES=30

# Rate Limiting
RATE_LIMIT_PER_MINUTE=60

# Database Configuration (optional - uses Supabase by default)
DATABASE_URL=

# File Upload Configuration
MAX_FILE_SIZE=10485760
ALLOWED_FILE_TYPES=pdf,txt,csv,md,log

# Chat Configuration
MAX_CONTEXT_LENGTH=4000
CHUNK_SIZE=1000
CHUNK_OVERLAP=200
SIMILARITY_THRESHOLD=0.7

# Email Configuration (optional)
SMTP_SERVER=smtp.gmail.com
SMTP_PORT=587
SMTP_USERNAME=<EMAIL>
SMTP_PASSWORD=your_app_password
