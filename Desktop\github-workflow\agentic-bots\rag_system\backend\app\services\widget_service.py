"""
Service for managing embeddable chat widgets.
"""
from typing import List, Optional, Dict, Any
import uuid
import secrets
from datetime import datetime
from urllib.parse import urlparse

from app.models.schemas import (
    ChatWidget, ChatWidgetCreate, ChatWidgetUpdate,
    PaginatedResponse
)
from app.database import get_supabase_client
import structlog

logger = structlog.get_logger()


class WidgetService:
    """Service for managing embeddable chat widgets."""
    
    # ... (create_widget, get_widget, get_widget_by_api_key, etc. remain the same) ...
    # PASTE THE EXISTING METHODS FROM YOUR FILE HERE, up to _domain_matches
    async def create_widget(self, widget_data: ChatWidgetCreate) -> ChatWidget:
        """Create a new chat widget."""
        try:
            supabase_client = await get_supabase_client()
            
            widget_dict = widget_data.model_dump()
            widget_dict["id"] = str(uuid.uuid4())
            widget_dict["chatbot_id"] = str(widget_data.chatbot_id)
            widget_dict["api_key"] = self._generate_api_key()
            widget_dict["created_at"] = datetime.utcnow().isoformat()
            widget_dict["updated_at"] = datetime.utcnow().isoformat()
            
            response = supabase_client.table("chat_widgets").insert(widget_dict).execute()
            
            if not response.data:
                raise Exception("Failed to create widget")
            
            return ChatWidget(**response.data[0])
            
        except Exception as e:
            logger.error(f"Error creating widget: {e}")
            raise
    
    async def get_widget(self, widget_id: uuid.UUID, chatbot_id: uuid.UUID) -> Optional[ChatWidget]:
        """Get a specific widget by ID."""
        try:
            supabase_client = await get_supabase_client()
            
            response = supabase_client.table("chat_widgets").select("*").eq("id", str(widget_id)).eq("chatbot_id", str(chatbot_id)).execute()
            
            if response.data:
                return ChatWidget(**response.data[0])
            return None
            
        except Exception as e:
            logger.error(f"Error getting widget: {e}")
            raise
    
    async def get_widget_by_api_key(self, api_key: str) -> Optional[ChatWidget]:
        """Get a widget by its API key."""
        try:
            supabase_client = await get_supabase_client()
            
            response = supabase_client.table("chat_widgets").select("*").eq("api_key", api_key).eq("is_active", True).execute()
            
            if response.data:
                return ChatWidget(**response.data[0])
            return None
            
        except Exception as e:
            logger.error(f"Error getting widget by API key: {e}")
            raise
    
    async def get_chatbot_widgets(
        self, 
        chatbot_id: uuid.UUID, 
        skip: int = 0, 
        limit: int = 10,
        active_only: bool = True
    ) -> PaginatedResponse:
        """Get all widgets for a chatbot with pagination."""
        try:
            supabase_client = await get_supabase_client()
            
            # Build query
            query = supabase_client.table("chat_widgets").select("*", count="exact").eq("chatbot_id", str(chatbot_id))
            
            if active_only:
                query = query.eq("is_active", True)
            
            # Get total count
            count_response = query.execute()
            total = len(count_response.data) if count_response.data else 0
            
            # Get paginated results
            query = query.range(skip, skip + limit - 1).order("created_at", desc=True)
            response = query.execute()
            
            widgets = [ChatWidget(**widget) for widget in response.data] if response.data else []
            
            return PaginatedResponse(
                items=widgets,
                total=total,
                page=skip // limit + 1,
                per_page=limit,
                pages=(total + limit - 1) // limit
            )
            
        except Exception as e:
            logger.error(f"Error getting chatbot widgets: {e}")
            raise
    
    async def update_widget(self, widget_id: uuid.UUID, chatbot_id: uuid.UUID, widget_data: ChatWidgetUpdate) -> Optional[ChatWidget]:
        """Update an existing widget."""
        try:
            supabase_client = await get_supabase_client()
            
            # Only update fields that are provided
            update_dict = {k: v for k, v in widget_data.model_dump().items() if v is not None}
            
            if not update_dict:
                # If no fields to update, return current widget
                return await self.get_widget(widget_id, chatbot_id)
            
            update_dict["updated_at"] = datetime.utcnow().isoformat()
            
            response = supabase_client.table("chat_widgets").update(update_dict).eq("id", str(widget_id)).eq("chatbot_id", str(chatbot_id)).execute()
            
            if response.data:
                return ChatWidget(**response.data[0])
            return None
            
        except Exception as e:
            logger.error(f"Error updating widget: {e}")
            raise
    
    async def delete_widget(self, widget_id: uuid.UUID, chatbot_id: uuid.UUID) -> bool:
        """Delete a widget (soft delete by setting is_active=False)."""
        try:
            supabase_client = await get_supabase_client()
            
            response = supabase_client.table("chat_widgets").update({
                "is_active": False,
                "updated_at": datetime.utcnow().isoformat()
            }).eq("id", str(widget_id)).eq("chatbot_id", str(chatbot_id)).execute()
            
            return bool(response.data)
            
        except Exception as e:
            logger.error(f"Error deleting widget: {e}")
            raise
    
    async def regenerate_api_key(self, widget_id: uuid.UUID, chatbot_id: uuid.UUID) -> Optional[ChatWidget]:
        """Regenerate API key for a widget."""
        try:
            supabase_client = await get_supabase_client()
            
            new_api_key = self._generate_api_key()
            
            response = supabase_client.table("chat_widgets").update({
                "api_key": new_api_key,
                "updated_at": datetime.utcnow().isoformat()
            }).eq("id", str(widget_id)).eq("chatbot_id", str(chatbot_id)).execute()
            
            if response.data:
                return ChatWidget(**response.data[0])
            return None
            
        except Exception as e:
            logger.error(f"Error regenerating API key: {e}")
            raise
    
    async def validate_widget_access(self, api_key: str, origin: Optional[str]) -> tuple[bool, Optional[ChatWidget]]:
        """Validate if a request from a specific origin is allowed for the widget."""
        try:
            widget = await self.get_widget_by_api_key(api_key)
            if not widget:
                logger.warning(f"Widget not found for API key: {api_key[:12]}...")
                return False, None

            if not widget.is_active:
                logger.warning(f"Widget is inactive: {api_key[:12]}...")
                return False, widget

            if not widget.allowed_domains:
                logger.info(f"Widget has no domain restrictions, allowing access: {api_key[:12]}...")
                return True, widget

            # Check if wildcard "*" is in allowed domains (allows all origins including null)
            if "*" in widget.allowed_domains:
                logger.info(f"Widget allows all domains (*): {api_key[:12]}...")
                return True, widget

            # Allow null origin for local file testing if localhost is in allowed domains
            if not origin or origin == "null":
                localhost_allowed = any(
                    domain.strip().lower() in ["localhost", "127.0.0.1", "*.localhost", "localhost:*"]
                    for domain in widget.allowed_domains
                )
                if localhost_allowed:
                    logger.info(f"Allowing null origin for localhost testing: {api_key[:12]}...")
                    return True, widget
                else:
                    logger.warning(f"Null origin not allowed, no localhost in domains: {api_key[:12]}...")
                    return False, widget

            try:
                parsed_origin = urlparse(origin)
                origin_domain = parsed_origin.netloc.lower()

                # Handle localhost with ports
                if origin_domain.startswith("localhost:") or origin_domain.startswith("127.0.0.1:"):
                    base_domain = origin_domain.split(":")[0]
                    localhost_allowed = any(
                        domain.strip().lower() in [base_domain, "localhost", "127.0.0.1", "*.localhost", "localhost:*"]
                        for domain in widget.allowed_domains
                    )
                    if localhost_allowed:
                        logger.info(f"Allowing localhost with port: {origin_domain}")
                        return True, widget

            except Exception as e:
                logger.error(f"Error parsing origin {origin}: {e}")
                return False, widget

            allowed = any(
                self._domain_matches(origin_domain, allowed_domain.strip().lower())
                for allowed_domain in widget.allowed_domains
            )

            if allowed:
                logger.info(f"Domain access allowed: {origin_domain}")
            else:
                logger.warning(f"Domain access denied: {origin_domain}, allowed: {widget.allowed_domains}")

            return allowed, widget

        except Exception as e:
            logger.error(f"Error validating widget access: {e}")
            return False, None

    def _domain_matches(self, origin_domain: str, allowed_domain: str) -> bool:
        """Check if origin domain matches allowed domain (supports wildcards)."""
        if allowed_domain == "*":
            return True
        if origin_domain == allowed_domain:
            return True
        if allowed_domain.startswith("*."):
            base_domain = allowed_domain[2:]
            return origin_domain.endswith(f".{base_domain}") or origin_domain == base_domain
        return False
    
    def _generate_api_key(self) -> str:
        """Generate a secure API key for the widget."""
        return f"widget_{secrets.token_urlsafe(32)}"

    def _generate_api_key(self) -> str:
        """Generate a secure API key for the widget."""
        return f"widget_{secrets.token_urlsafe(32)}"

    async def generate_widget_script(self, api_key: str) -> Optional[str]:
        """Generates a self-contained JavaScript widget with the same design as chatbot.html."""
        widget = await self.get_widget_by_api_key(api_key)
        if not widget or not widget.is_active:
            return None  # Return None for invalid/inactive widgets

        # Get widget configuration
        config = widget.widget_config or {}
        appearance = config.get("appearance", {})
        behavior = config.get("behavior", {})
        branding = config.get("branding", {})

        # Extract configuration values with defaults
        theme = appearance.get('theme', 'light')
        primary_color = appearance.get('primaryColor', '#6D4FC2')
        secondary_color = appearance.get('secondaryColor', '#F6F2FF')
        position = appearance.get('position', 'bottom-right')
        width = appearance.get('width', '420px')
        height = appearance.get('height', '600px')
        z_index = appearance.get('zIndex', 999999)

        welcome_message = behavior.get('welcomeMessage', 'Hey there 👋\\nHow can I help you today?')
        auto_open = behavior.get('autoOpen', False)
        auto_open_delay = behavior.get('autoOpenDelay', 5000)

        company_name = branding.get('companyName', 'Chatbot')
        show_powered_by = branding.get('showPoweredBy', True)

        base_url = "http://localhost:8000"

        return f"""
(function() {{
    'use strict';

    // Prevent multiple widget instances
    if (window.aiChatWidgetLoaded) return;
    window.aiChatWidgetLoaded = true;

    // Create widget container
    const widgetContainer = document.createElement('div');
    widgetContainer.id = 'ai-chatbot-widget';
    widgetContainer.className = '{theme}';
    document.body.appendChild(widgetContainer);

    // Add CSS styles
    const style = document.createElement('style');
    style.textContent = `
        /* All styles are scoped to the #ai-chatbot-widget container to prevent conflicts with host pages. */
        #ai-chatbot-widget {{
            --primary-color: {primary_color};
            --secondary-color: {secondary_color};
            --header-bg: var(--primary-color);
            --widget-bg: #fff;
            --text-color: #333;
            --text-color-light: #fff;
            --border-color: #ddd;
            --input-bg: #fff;
            --scrollbar-color: #DDD3F9;
            font-family: "Inter", -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
        }}

        #ai-chatbot-widget.dark {{
            --primary-color: {primary_color};
            --secondary-color: #2c2c2e;
            --header-bg: #1e1e1e;
            --widget-bg: #242424;
            --text-color: #f0f0f0;
            --text-color-light: #f0f0f0;
            --border-color: #444;
            --input-bg: #333;
            --scrollbar-color: #4a4a4a;
        }}

        #ai-chatbot-widget * {{
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }}

        /* --- Icon Font Fix --- */
        #ai-chatbot-widget .material-symbols-rounded {{
            font-family: 'Material Symbols Rounded', sans-serif;
            font-weight: normal;
            font-style: normal;
            font-size: 24px;
            line-height: 1;
            letter-spacing: normal;
            text-transform: none;
            display: inline-block;
            white-space: nowrap;
            word-wrap: normal;
            direction: ltr;
            -webkit-font-feature-settings: 'liga';
            -webkit-font-smoothing: antialiased;
            user-select: none;
        }}

        /* --- Chatbot Toggler --- */
        #ai-chatbot-widget #chatbot-toggler {{
            position: fixed;
            {self._get_position_styles(position)}
            height: 50px;
            width: 50px;
            border: none;
            outline: none;
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 50%;
            background: var(--primary-color);
            color: var(--text-color-light);
            transition: all 0.2s ease;
            z-index: {z_index};
        }}

        #ai-chatbot-widget.show-chatbot #chatbot-toggler {{
            transform: rotate(90deg);
        }}

        #ai-chatbot-widget #chatbot-toggler span {{
            position: absolute;
            transition: opacity 0.2s ease;
        }}

        #ai-chatbot-widget.show-chatbot #chatbot-toggler span:first-child,
        #ai-chatbot-widget #chatbot-toggler span:last-child {{
            opacity: 0;
        }}

        #ai-chatbot-widget.show-chatbot #chatbot-toggler span:last-child {{
            opacity: 1;
        }}

        /* --- Chatbot Popup --- */
        #ai-chatbot-widget .chatbot-popup {{
            position: fixed;
            {self._get_popup_position_styles(position)}
            width: {width};
            height: {height};
            background: var(--widget-bg);
            border-radius: 15px;
            box-shadow: 0 0 128px 0 rgba(0, 0, 0, 0.1),
                0 32px 64px -48px rgba(0, 0, 0, 0.5);
            transform: scale(0.5);
            opacity: 0;
            pointer-events: none;
            transform-origin: {self._get_transform_origin(position)};
            transition: all 0.15s ease;
            color: var(--text-color);
            z-index: {z_index - 1};
        }}

        #ai-chatbot-widget.show-chatbot .chatbot-popup {{
            transform: scale(1);
            opacity: 1;
            pointer-events: auto;
        }}

        #ai-chatbot-widget .chatbot-popup .chat-header {{
            background: var(--header-bg);
            padding: 16px 20px;
            border-radius: 15px 15px 0 0;
            text-align: center;
            color: var(--text-color-light);
            display: flex;
            align-items: center;
            justify-content: space-between;
        }}

        #ai-chatbot-widget .chat-header .header-info {{
            display: flex;
            align-items: center;
            gap: 10px;
        }}

        #ai-chatbot-widget .header-info .logo-text {{
            font-size: 1.4rem;
            font-weight: 600;
        }}

        #ai-chatbot-widget .chat-header .chatbot-icon {{
             background: var(--text-color-light);
             color: var(--primary-color);
        }}

        #ai-chatbot-widget .chat-header .close-btn {{
            font-size: 1.9rem;
            cursor: pointer;
        }}

        /* --- Chat Body --- */
        #ai-chatbot-widget .chatbot-popup .chat-body {{
            height: 510px;
            padding: 30px 20px 100px;
            overflow-y: auto;
            display: flex;
            flex-direction: column;
            gap: 20px;
            scrollbar-width: thin;
            scrollbar-color: var(--scrollbar-color) transparent;
        }}

        #ai-chatbot-widget .chat-body .message {{
            display: flex;
            gap: 10px;
            align-items: flex-start;
        }}

        #ai-chatbot-widget .chat-body .message.bot-message {{
            justify-content: flex-start;
        }}

        #ai-chatbot-widget .chat-body .message.user-message {{
            flex-direction: row-reverse;
        }}

        #ai-chatbot-widget .chatbot-icon {{
            height: 35px;
            width: 35px;
            color: var(--text-color-light);
            background: var(--primary-color);
            padding: 6px;
            border-radius: 50%;
            flex-shrink: 0;
        }}

        #ai-chatbot-widget .chat-body .message-text {{
            padding: 12px 16px;
            max-width: 75%;
            word-wrap: break-word;
            white-space: pre-line;
            font-size: 0.95rem;
        }}

        #ai-chatbot-widget .chat-body .bot-message .message-text {{
            background: var(--secondary-color);
            color: var(--text-color);
            border-radius: 12px 12px 12px 0;
        }}

        #ai-chatbot-widget .chat-body .user-message .message-text {{
            background: var(--primary-color);
            color: var(--text-color-light);
            border-radius: 12px 12px 0 12px;
        }}

        /* --- Chat Footer --- */
        #ai-chatbot-widget .chatbot-popup .chat-footer {{
            position: absolute;
            bottom: 0;
            width: 100%;
            background: var(--widget-bg);
            padding: 3px 20px 10px;
            border-top: 1px solid var(--border-color);
        }}

        #ai-chatbot-widget .chat-footer .chat-form {{
            display: flex;
            align-items: center;
            gap: 10px;
            border: 1px solid var(--border-color);
            border-radius: 6px;
            padding: 5px 12px;
            background: var(--input-bg);
        }}

        #ai-chatbot-widget .chat-form .message-input {{
            border: none;
            outline: none;
            width: 100%;
            padding: 10px 0;
            font-size: 0.95rem;
            background: none;
            color: var(--text-color);
        }}

        #ai-chatbot-widget .chat-form .send-btn {{
            font-size: 1.75rem;
            cursor: pointer;
            color: var(--primary-color);
            background: none;
            border: none;
            outline: none;
        }}

        /* --- Responsive Design --- */
        @media(max-width: 600px) {{
            #ai-chatbot-widget .chatbot-popup {{
                right: 0;
                bottom: 0;
                width: 100%;
                height: 100%;
                border-radius: 0;
            }}

            #ai-chatbot-widget .chatbot-popup .chat-header {{
                 border-radius: 0;
            }}

            #ai-chatbot-widget .chatbot-popup .chat-body {{
                height: 90%;
            }}

            #ai-chatbot-widget.show-chatbot #chatbot-toggler {{
                display: none;
            }}
        }}
    `;
    document.head.appendChild(style);

    // Load Google Fonts and Icons
    const fontLink1 = document.createElement('link');
    fontLink1.rel = 'stylesheet';
    fontLink1.href = 'https://fonts.googleapis.com/css2?family=Material+Symbols+Rounded:opsz,wght,FILL,GRAD@20..48,100..700,0..1,-50..200';
    document.head.appendChild(fontLink1);

    const fontLink2 = document.createElement('link');
    fontLink2.rel = 'preconnect';
    fontLink2.href = 'https://fonts.googleapis.com';
    document.head.appendChild(fontLink2);

    const fontLink3 = document.createElement('link');
    fontLink3.rel = 'preconnect';
    fontLink3.href = 'https://fonts.gstatic.com';
    fontLink3.crossOrigin = 'anonymous';
    document.head.appendChild(fontLink3);

    const fontLink4 = document.createElement('link');
    fontLink4.rel = 'stylesheet';
    fontLink4.href = 'https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600&display=swap';
    document.head.appendChild(fontLink4);

    // Create widget HTML structure
    widgetContainer.innerHTML = `
        <button id="chatbot-toggler">
            <span class="material-symbols-rounded">mode_comment</span>
            <span class="material-symbols-rounded">close</span>
        </button>

        <div class="chatbot-popup">
            <div class="chat-header">
                <div class="header-info">
                    <svg xmlns="http://www.w3.org/2000/svg" class="chatbot-icon" viewBox="0 0 24 24" fill="currentColor">
                        <path d="M12 2C6.48 2 2 5.79 2 10.5c0 2.22 1.01 4.25 2.68 5.72l-1.68 1.68c-.14.14-.14.36 0 .5.07.07.16.1.25.1s.18-.03.25-.1l1.93-1.93C7.06 17.58 9.42 18.5 12 18.5c5.52 0 10-3.79 10-8.5S17.52 2 12 2zm1 11h-2v-2h2v2zm0-4h-2V6h2v3z" />
                    </svg>
                    <h2 class="logo-text">{company_name}</h2>
                </div>
                <span class="material-symbols-rounded close-btn">close</span>
            </div>
            <div class="chat-body" id="chat-body">
                <div class="message bot-message">
                    <svg xmlns="http://www.w3.org/2000/svg" class="chatbot-icon" viewBox="0 0 24 24" fill="currentColor">
                        <path d="M12 2C6.48 2 2 5.79 2 10.5c0 2.22 1.01 4.25 2.68 5.72l-1.68 1.68c-.14.14-.14.36 0 .5.07.07.16.1.25.1s.18-.03.25-.1l1.93-1.93C7.06 17.58 9.42 18.5 12 18.5c5.52 0 10-3.79 10-8.5S17.52 2 12 2zm1 11h-2v-2h2v2zm0-4h-2V6h2v3z" />
                    </svg>
                    <div class="message-text">{welcome_message}</div>
                </div>
            </div>
            <div class="chat-footer">
                <form class="chat-form" id="chat-form">
                    <input
                        class="message-input"
                        id="message-input"
                        placeholder="Enter a message..."
                        required
                    />
                    <button type="submit" class="material-symbols-rounded send-btn">send</button>
                </form>
                {self._get_powered_by_footer(show_powered_by)}
            </div>
        </div>
    `;

    // Widget functionality
    class ChatWidget {{
        constructor() {{
            this.apiKey = '{api_key}';
            this.sessionId = this.generateSessionId();
            this.showChatbot = false;
            this.chatHistory = [];
            this.autoOpen = {str(auto_open).lower()};
            this.autoOpenDelay = {auto_open_delay};

            this.initializeElements();
            this.initializeEventListeners();
            this.setupAutoOpen();
        }}

        generateSessionId() {{
            return 'session_' + Math.random().toString(36).substr(2, 9) + '_' + Date.now();
        }}

        initializeElements() {{
            this.toggler = document.getElementById('chatbot-toggler');
            this.popup = document.querySelector('.chatbot-popup');
            this.chatBody = document.getElementById('chat-body');
            this.messageInput = document.getElementById('message-input');
            this.chatForm = document.getElementById('chat-form');
            this.closeBtn = document.querySelector('.close-btn');
        }}

        initializeEventListeners() {{
            this.toggler.addEventListener('click', () => this.toggleChatbot());
            this.closeBtn.addEventListener('click', () => this.toggleChatbot());
            this.chatForm.addEventListener('submit', (e) => this.handleFormSubmit(e));
        }}

        setupAutoOpen() {{
            if (this.autoOpen) {{
                setTimeout(() => {{
                    this.toggleChatbot();
                }}, this.autoOpenDelay);
            }}
        }}

        toggleChatbot() {{
            this.showChatbot = !this.showChatbot;
            widgetContainer.classList.toggle('show-chatbot', this.showChatbot);
        }}

        async handleFormSubmit(e) {{
            e.preventDefault();
            const userMessage = this.messageInput.value.trim();
            if (!userMessage) return;

            this.addMessage(userMessage, 'user');
            this.messageInput.value = '';

            // Show thinking message
            const thinkingMessage = this.addMessage('Thinking...', 'bot');

            try {{
                const response = await fetch('{base_url}/api/v1/widgets/chat', {{
                    method: 'POST',
                    headers: {{
                        'Content-Type': 'application/json',
                        'Authorization': `Bearer ${{this.apiKey}}`
                    }},
                    body: JSON.stringify({{
                        message: userMessage,
                        session_id: this.sessionId
                    }})
                }});

                if (!response.ok) {{
                    throw new Error(`HTTP error! status: ${{response.status}}`);
                }}

                const data = await response.json();

                // Remove thinking message and add real response
                thinkingMessage.remove();
                this.addMessage(data.response, 'bot');

            }} catch (error) {{
                console.error('Error sending message:', error);
                thinkingMessage.remove();
                this.addMessage('Sorry, I encountered an error. Please try again.', 'bot');
            }}
        }}

        addMessage(text, role) {{
            const messageDiv = document.createElement('div');
            messageDiv.className = `message ${{role}}-message`;

            if (role === 'bot') {{
                messageDiv.innerHTML = `
                    <svg xmlns="http://www.w3.org/2000/svg" class="chatbot-icon" viewBox="0 0 24 24" fill="currentColor">
                        <path d="M12 2C6.48 2 2 5.79 2 10.5c0 2.22 1.01 4.25 2.68 5.72l-1.68 1.68c-.14.14-.14.36 0 .5.07.07.16.1.25.1s.18-.03.25-.1l1.93-1.93C7.06 17.58 9.42 18.5 12 18.5c5.52 0 10-3.79 10-8.5S17.52 2 12 2zm1 11h-2v-2h2v2zm0-4h-2V6h2v3z" />
                    </svg>
                    <div class="message-text">${{text}}</div>
                `;
            }} else {{
                messageDiv.innerHTML = `<div class="message-text">${{text}}</div>`;
            }}

            this.chatBody.appendChild(messageDiv);
            this.chatBody.scrollTop = this.chatBody.scrollHeight;

            return messageDiv;
        }}
    }}

    // Initialize the widget when DOM is ready
    if (document.readyState === 'loading') {{
        document.addEventListener('DOMContentLoaded', function() {{
            new ChatWidget();
        }});
    }} else {{
        new ChatWidget();
    }}
}})();
"""

    async def get_widget_embed_code(self, widget_id: uuid.UUID, chatbot_id: uuid.UUID) -> Dict[str, str]:
        """Generate embed code for the widget."""
        try:
            widget = await self.get_widget(widget_id, chatbot_id)
            if not widget:
                raise ValueError(f"Widget {widget_id} not found")

            # Use localhost as default base URL since api_base_url is not in settings
            base_url = "http://localhost:8000"

            # Script tag version
            script_url = f"{base_url}/api/v1/widgets/script.js?key={widget.api_key}"
            script_code = f"""<!-- AI Chatbot Widget -->
<script src="{script_url}" async defer></script>"""

            # Basic embed script
            embed_script = f"""
<!-- AI Chatbot Widget -->
<iframe
    src="{base_url}/widget/{widget.api_key}"
    width="400"
    height="600"
    frameborder="0"
    style="position: fixed; bottom: 20px; right: 20px; z-index: 9999; border-radius: 10px; box-shadow: 0 4px 20px rgba(0,0,0,0.15);">
</iframe>
"""

            # React component code
            react_code = f"""
// React Component for AI Chat Widget
function AIChatWidget() {{
    return (
        <iframe
            src="{base_url}/widget/{widget.api_key}"
            style={{{{
                position: 'fixed',
                bottom: '20px',
                right: '20px',
                width: '400px',
                height: '600px',
                border: 'none',
                borderRadius: '10px',
                boxShadow: '0 4px 20px rgba(0,0,0,0.15)',
                zIndex: 9999
            }}}}
            frameBorder="0"
        />
    );
}}
"""

            # Configuration for different frameworks
            configs = {
                "script_tag": script_code.strip(),
                "iframe_tag": embed_script.strip(),
                "react_component": react_code.strip(),
                "configuration": widget.widget_config
            }

            return configs

        except Exception as e:
            logger.error(f"Error generating embed code: {e}")
            raise

    async def get_widget_analytics(self, widget_id: uuid.UUID, chatbot_id: uuid.UUID, days: int = 30) -> Dict[str, Any]:
        """Get analytics data for a widget."""
        try:
            # This would typically query analytics data from a separate analytics service
            # For now, return mock data structure
            
            analytics = {
                "widget_id": str(widget_id),
                "period_days": days,
                "total_conversations": 0,
                "total_messages": 0,
                "average_session_duration": 0,
                "popular_questions": [],
                "daily_stats": [],
                "domain_stats": {},
                "action_executions": 0
            }
            
            # In a real implementation, you would:
            # 1. Query conversation logs
            # 2. Calculate metrics
            # 3. Return real analytics data
            
            return analytics
            
        except Exception as e:
            logger.error(f"Error getting widget analytics: {e}")
            raise
    
    async def customize_widget_appearance(
        self, 
        widget_id: uuid.UUID, 
        chatbot_id: uuid.UUID, 
        appearance_config: Dict[str, Any]
    ) -> Optional[ChatWidget]:
        """Update widget appearance configuration."""
        try:
            widget = await self.get_widget(widget_id, chatbot_id)
            if not widget:
                return None
            
            # Merge appearance config with existing widget config
            current_config = widget.widget_config.copy()
            current_config.update({
                "appearance": appearance_config,
                "updated_at": datetime.utcnow().isoformat()
            })
            
            update_data = ChatWidgetUpdate(widget_config=current_config)
            return await self.update_widget(widget_id, chatbot_id, update_data)
            
        except Exception as e:
            logger.error(f"Error customizing widget appearance: {e}")
            raise

    def get_default_widget_config(self) -> Dict[str, Any]:
        """Get default widget configuration."""
        return {
            "appearance": {
                "theme": "light",
                "primaryColor": "#6D4FC2",
                "secondaryColor": "#F6F2FF",
                "position": "bottom-right",
                "width": "420px",
                "height": "600px",
                "zIndex": 999999
            },
            "behavior": {
                "autoOpen": False,
                "autoOpenDelay": 5000,
                "welcomeMessage": "Hey there 👋\nHow can I help you today?",
                "enableNotifications": True,
            },
            "features": {
                "enableTypingIndicator": True,
            },
            "branding": {
                "showPoweredBy": True,
                "companyName": "Chatbot"
            }
        }

    def _get_position_styles(self, position: str) -> str:
        """Get CSS positioning styles based on position setting."""
        if position == "bottom-left":
            return "left: 35px; bottom: 30px;"
        elif position == "top-right":
            return "right: 35px; top: 30px;"
        elif position == "top-left":
            return "left: 35px; top: 30px;"
        else:  # default to bottom-right
            return "right: 35px; bottom: 30px;"

    def _get_popup_position_styles(self, position: str) -> str:
        """Get CSS positioning styles for popup based on position setting."""
        if position == "bottom-left":
            return "left: 35px; bottom: 90px;"
        elif position == "top-right":
            return "right: 35px; top: 90px;"
        elif position == "top-left":
            return "left: 35px; top: 90px;"
        else:  # default to bottom-right
            return "right: 35px; bottom: 90px;"

    def _get_transform_origin(self, position: str) -> str:
        """Get CSS transform origin based on position setting."""
        if position == "bottom-left":
            return "bottom left"
        elif position == "top-right":
            return "top right"
        elif position == "top-left":
            return "top left"
        else:  # default to bottom-right
            return "bottom right"

    def _get_powered_by_footer(self, show_powered_by: bool) -> str:
        """Get powered by footer HTML if enabled."""
        if not show_powered_by:
            return ""
        return '''
                <div style="text-align: center; padding: 8px; font-size: 11px; color: #888; border-top: 1px solid var(--border-color);">
                    Powered by <a href="#" style="color: var(--primary-color); text-decoration: none;">AI Assistant</a>
                </div>'''


# Global widget service instance
widget_service = WidgetService()

async def get_widget_service() -> WidgetService:
    """Dependency to get widget service."""
    return widget_service