"""
Chat API endpoints for conversational interactions.
"""
from typing import Optional
import uuid
from fastapi import API<PERSON>out<PERSON>, Depends, HTTPException, Request
from fastapi.responses import StreamingResponse

from app.models.schemas import (
    ChatMessageCreate, ChatMessageResponse, 
    SearchQuery, SearchResult, APIResponse
)
from app.services.chat_service import ChatService, get_chat_service
from app.services.widget_service import WidgetService, get_widget_service
import structlog

logger = structlog.get_logger()

router = APIRouter(prefix="/chat", tags=["chat"])


@router.post("/{chatbot_id}", response_model=ChatMessageResponse)
async def send_message(
    chatbot_id: uuid.UUID,
    message_data: ChatMessageCreate,
    chat_service: ChatService = Depends(get_chat_service)
):
    """Send a message to a chatbot and get a response."""
    try:
        response = await chat_service.process_message_legacy(
            message=message_data.message,
            chatbot_id=chatbot_id,
            user_id=message_data.user_id,
            session_id=message_data.session_id
        )
        return response
    except Exception as e:
        logger.error(f"Error processing message: {e}")
        raise HTTPException(status_code=500, detail="Error processing message")


@router.post("/widget/{api_key}", response_model=ChatMessageResponse)
async def widget_chat(
    api_key: str,
    message_data: ChatMessageCreate,
    request: Request,
    chat_service: ChatService = Depends(get_chat_service),
    widget_service: WidgetService = Depends(get_widget_service)
):
    """Chat endpoint for embeddable widgets."""
    try:
        # Validate widget access
        origin = request.headers.get("origin", "")
        is_allowed, widget = await widget_service.validate_widget_access(api_key, origin)
        
        if not is_allowed:
            raise HTTPException(status_code=403, detail="Access denied for this domain")
        
        if not widget:
            raise HTTPException(status_code=404, detail="Widget not found")
        
        # Process message
        response = await chat_service.process_message_legacy(
            message=message_data.message,
            chatbot_id=widget.chatbot_id,
            user_id=message_data.user_id,
            session_id=message_data.session_id
        )
        
        return response
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error in widget chat: {e}")
        raise HTTPException(status_code=500, detail="Error processing message")


@router.post("/{chatbot_id}/search")
async def search_knowledge_base(
    chatbot_id: uuid.UUID,
    search_query: SearchQuery,
    chat_service: ChatService = Depends(get_chat_service)
):
    """Search the chatbot's knowledge base."""
    try:
        results = await chat_service.search_knowledge_base(
            query=search_query.query,
            chatbot_id=chatbot_id,
            k=search_query.k
        )
        
        return APIResponse(
            success=True,
            message="Search completed",
            data={"results": results, "count": len(results)}
        )
        
    except Exception as e:
        logger.error(f"Error searching knowledge base: {e}")
        raise HTTPException(status_code=500, detail="Error searching knowledge base")


@router.get("/{chatbot_id}/stream/{session_id}")
async def stream_chat_updates(
    chatbot_id: uuid.UUID,
    session_id: str
):
    """Stream chat updates for real-time communication (WebSocket alternative)."""
    # This would implement Server-Sent Events for real-time updates
    # In a production environment, you might want to use WebSockets instead
    
    async def generate_updates():
        # Mock implementation - in reality, you'd listen to events
        yield "data: {\"type\": \"connected\", \"session_id\": \"" + session_id + "\"}\n\n"
        
        # Keep connection alive
        import asyncio
        while True:
            await asyncio.sleep(30)
            yield "data: {\"type\": \"heartbeat\"}\n\n"
    
    return StreamingResponse(
        generate_updates(),
        media_type="text/event-stream",
        headers={
            "Cache-Control": "no-cache",
            "Connection": "keep-alive",
            "Access-Control-Allow-Origin": "*",
        }
    )


@router.post("/{chatbot_id}/feedback")
async def submit_feedback(
    chatbot_id: uuid.UUID,
    feedback_data: dict
):
    """Submit feedback for a chat response."""
    try:
        # In a real implementation, you would store this in a feedback table
        logger.info(f"Feedback received for chatbot {chatbot_id}: {feedback_data}")
        
        return APIResponse(
            success=True,
            message="Feedback submitted successfully",
            data={"feedback_id": str(uuid.uuid4())}
        )
        
    except Exception as e:
        logger.error(f"Error submitting feedback: {e}")
        raise HTTPException(status_code=500, detail="Error submitting feedback")
